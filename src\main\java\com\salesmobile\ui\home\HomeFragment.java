package com.salesmobile.ui.home;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.room.Room;

import com.salesmobile.AppDatabase;
import com.salesmobile.IndicesApiClient;
import com.salesmobile.R;
import com.salesmobile.TokenManager;
import com.salesmobile.config.SharedPreferences;
import com.salesmobile.databinding.FragmentHomeBinding;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class HomeFragment extends Fragment {
    private static final String TAG = "HomeFragment";
    private HomeViewModel homeViewModel;
    private AppDatabase db;
    private SharedPreferences sharedPrefs;
    private IndicesApiClient indicesApiClient;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        db = AppDatabase.getInstance(requireContext());
        sharedPrefs = new SharedPreferences(requireContext());
        indicesApiClient = new IndicesApiClient(requireContext());
    }

    public View onCreateView(@NonNull LayoutInflater inflater,
                             ViewGroup container, Bundle savedInstanceState) {
        homeViewModel = new ViewModelProvider(this).get(HomeViewModel.class);
        View root = inflater.inflate(R.layout.fragment_home, container, false);

        // Referencias a los elementos de la UI
        final TextView tvNombreSucursal = root.findViewById(R.id.tv_nombre_sucursal);
        final TextView tvFecha = root.findViewById(R.id.tv_fecha);
        final TextView tvTotal = root.findViewById(R.id.tv_total);
        final ProgressBar progressBar = root.findViewById(R.id.progressBar);
        final TextView tvTransacciones = root.findViewById(R.id.tv_transacciones);
        final TextView tvTicketMedio = root.findViewById(R.id.tv_ticket_medio);
        final TextView tvCotizacion = root.findViewById(R.id.tv_cotizacion);

        homeViewModel.getFecha().observe(getViewLifecycleOwner(), tvFecha::setText);
        homeViewModel.getTotalVentas().observe(getViewLifecycleOwner(), tvTotal::setText);
        homeViewModel.getTransacciones().observe(getViewLifecycleOwner(), tvTransacciones::setText);
        homeViewModel.getTicketMedio().observe(getViewLifecycleOwner(), tvTicketMedio::setText);



        homeViewModel.getIsLoading().observe(getViewLifecycleOwner(), isLoading -> {
            if (isLoading) {
                progressBar.setVisibility(View.VISIBLE);
                tvTotal.setVisibility(View.INVISIBLE);
                tvTransacciones.setVisibility(View.INVISIBLE);
                tvTicketMedio.setVisibility(View.INVISIBLE);
            } else {
                progressBar.setVisibility(View.GONE);
                tvTotal.setVisibility(View.VISIBLE);
                tvTransacciones.setVisibility(View.VISIBLE);
                tvTicketMedio.setVisibility(View.VISIBLE);
            }
        });

        // Obtener la instancia de tu base de datos (ajusta esto según tu implementación)
       // AppDatabase db = Room.databaseBuilder(requireContext(),
        //        AppDatabase.class, "app_database").build();

        homeViewModel.cargarDatosDelDia(db);

        // Cargar nombre de la sucursal
        cargarNombreSucursal(tvNombreSucursal);

        // Cargar cotización del día
        cargarCotizacion(tvCotizacion);

        return root;
    }

    private void cargarNombreSucursal(TextView tvNombreSucursal) {
        try {
            SharedPreferences.SucursalInfo sucursalInfo = sharedPrefs.getSucursalInfo();
            if (sucursalInfo != null && !sucursalInfo.getNombre().isEmpty()) {
                tvNombreSucursal.setText(sucursalInfo.getNombre());
                Log.d(TAG, "Nombre de sucursal cargado: " + sucursalInfo.getNombre());
            } else {
                tvNombreSucursal.setText("Sucursal Principal");
                Log.d(TAG, "No se encontró información de sucursal, usando valor por defecto");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error al cargar nombre de sucursal", e);
            tvNombreSucursal.setText("Sucursal Principal");
        }
    }

    private void cargarCotizacion(TextView tvCotizacion) {
        // Primero mostrar la cotización guardada
        mostrarCotizacionGuardada(tvCotizacion);

        // Luego intentar actualizar con datos frescos del API
        actualizarCotizacionDesdeAPI(tvCotizacion);
    }

    private void mostrarCotizacionGuardada(TextView tvCotizacion) {
        try {
            float cotizacion = sharedPrefs.getCotizacion();
            DecimalFormat formatter = new DecimalFormat("#,##0.00");
            tvCotizacion.setText("Gs. " + formatter.format(cotizacion));

            Log.d(TAG, "Cotización guardada mostrada: " + cotizacion);
        } catch (Exception e) {
            Log.e(TAG, "Error al mostrar cotización guardada", e);
            tvCotizacion.setText("Gs. 1.00");
        }
    }

    private void actualizarCotizacionDesdeAPI(TextView tvCotizacion) {
        TokenManager tokenManager = TokenManager.getInstance();
        String token = tokenManager.getToken();

        if (token == null) {
            Log.d(TAG, "No hay token disponible, solicitando nuevo token");
            tokenManager.requestToken(requireContext(), () -> {
                String newToken = tokenManager.getToken();
                if (newToken != null) {
                    fetchCotizacion(newToken, tvCotizacion);
                } else {
                    Log.e(TAG, "No se pudo obtener token para actualizar cotización");
                }
            });
        } else {
            fetchCotizacion(token, tvCotizacion);
        }
    }

    private void fetchCotizacion(String token, TextView tvCotizacion) {
        indicesApiClient.fetchIndicesAndSaveValor1(token, new IndicesApiClient.IndicesCallback() {
            @Override
            public void onSuccess(double valor1) {
                if (isAdded()) {
                    requireActivity().runOnUiThread(() -> {
                        try {
                            DecimalFormat formatter = new DecimalFormat("#,##0.00");
                            tvCotizacion.setText("Gs. " + formatter.format(valor1));

                            Log.d(TAG, "Cotización actualizada desde API: " + valor1);
                        } catch (Exception e) {
                            Log.e(TAG, "Error al actualizar UI con nueva cotización", e);
                        }
                    });
                }
            }

            @Override
            public void onError(String message) {
                Log.e(TAG, "Error al obtener cotización desde API: " + message);
                // No mostramos toast para no molestar al usuario, la cotización guardada ya se muestra
            }
        });
    }
}