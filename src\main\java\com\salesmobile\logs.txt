2025-07-30 12:55:18.042 16617-19424 IndicesApiClient        com.salesmobile                      D  Valor1 guardado correctamente: 7390.0
2025-07-30 12:55:18.042 16617-19424 DatabaseSynchronizer    com.salesmobile                      I  Índices sincronizados. Valor1: 7390.0
2025-07-30 12:55:18.465 16617-16617 MiuiStubRegistry        com.salesmobile                      W  Failed to collect stub providers in android.magicpointer.util.MiuiMagicPointerUtilsStubHeadManifest$$: /system_ext/framework/miui-framework-pointer-pad.jar not exist
2025-07-30 12:55:18.474 16617-16617 BLASTBufferQueue        com.salesmobile                      D  [VRI[LoginActivity]#0](f:0,a:1) destructor()
2025-07-30 12:55:18.474 16617-16617 BufferQueueConsumer     com.salesmobile                      D  [VRI[LoginActivity]#0(BLAST Consumer)0](id:40e900000000,api:0,p:-1,c:16617) disconnect
2025-07-30 12:55:18.479 16617-16617 WindowOnBackDispatcher  com.salesmobile                      W  sendCancelIfRunning: isInProgress=falsecallback=android.app.Activity$$ExternalSyntheticLambda0@1d0cd0
2025-07-30 12:55:18.484 16617-16617 View                    com.salesmobile                      D  [Warning] assignParent to null: this = DecorView@9fdc932[LoginActivity]
2025-07-30 12:55:20.018 16617-19452 ProfileInstaller        com.salesmobile                      D  Installing profile for com.salesmobile
2025-07-30 12:55:27.870 16617-19438 IndicesApiClient        com.salesmobile                      E  Error de red: timeout (Ask Gemini)
java.net.SocketTimeoutException: timeout
	at okio.SocketAsyncTimeout.newTimeoutException(JvmOkio.kt:146)
	at okio.AsyncTimeout.access$newTimeoutException(AsyncTimeout.kt:161)
	at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:339)
	at okio.RealBufferedSource.read(RealBufferedSource.kt:192)
	at okhttp3.internal.http1.Http1ExchangeCodec$AbstractSource.read(Http1ExchangeCodec.kt:339)
	at okhttp3.internal.http1.Http1ExchangeCodec$ChunkedSource.read(Http1ExchangeCodec.kt:420)
	at okhttp3.internal.connection.Exchange$ResponseBodySource.read(Exchange.kt:281)
	at okio.Buffer.writeAll(Buffer.kt:1303)
	at okio.RealBufferedSource.readString(RealBufferedSource.kt:96)
	at okhttp3.ResponseBody.string(ResponseBody.kt:187)
	at com.salesmobile.IndicesApiClient.lambda$fetchIndicesAndSaveValor1$0$com-salesmobile-IndicesApiClient(IndicesApiClient.java:82)
	at com.salesmobile.IndicesApiClient$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)
Caused by: java.net.SocketException: Socket closed
	at java.net.SocketInputStream.read(SocketInputStream.java:188)
	at java.net.SocketInputStream.read(SocketInputStream.java:143)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:989)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:953)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readUntilDataAvailable(ConscryptEngineSocket.java:868)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.read(ConscryptEngineSocket.java:841)
	at okio.InputStreamSource.read(JvmOkio.kt:93)
	at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:128)
	at okio.RealBufferedSource.read(RealBufferedSource.kt:192) 
	at okhttp3.internal.http1.Http1ExchangeCodec$AbstractSource.read(Http1ExchangeCodec.kt:339) 
	at okhttp3.internal.http1.Http1ExchangeCodec$ChunkedSource.read(Http1ExchangeCodec.kt:420) 
	at okhttp3.internal.connection.Exchange$ResponseBodySource.read(Exchange.kt:281) 
	at okio.Buffer.writeAll(Buffer.kt:1303) 
	at okio.RealBufferedSource.readString(RealBufferedSource.kt:96) 
	at okhttp3.ResponseBody.string(ResponseBody.kt:187) 
	at com.salesmobile.IndicesApiClient.lambda$fetchIndicesAndSaveValor1$0$com-salesmobile-IndicesApiClient(IndicesApiClient.java:82) 
	at com.salesmobile.IndicesApiClient$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0) 
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) 
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644) 
	at java.lang.Thread.run(Thread.java:1012) 
2025-07-30 12:55:27.871 16617-19438 HomeFragment            com.salesmobile                      E  Error al obtener cotización desde API: Error de red: timeout
2025-07-30 12:55:27.933 16617-19444 IndicesApiClient        com.salesmobile                      E  Error de red: timeout (Ask Gemini)
java.net.SocketTimeoutException: timeout
	at okio.SocketAsyncTimeout.newTimeoutException(JvmOkio.kt:146)
	at okio.AsyncTimeout.access$newTimeoutException(AsyncTimeout.kt:161)
	at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:339)
	at okio.RealBufferedSource.read(RealBufferedSource.kt:192)
	at okhttp3.internal.http1.Http1ExchangeCodec$AbstractSource.read(Http1ExchangeCodec.kt:339)
	at okhttp3.internal.http1.Http1ExchangeCodec$ChunkedSource.read(Http1ExchangeCodec.kt:420)
	at okhttp3.internal.connection.Exchange$ResponseBodySource.read(Exchange.kt:281)
	at okio.Buffer.writeAll(Buffer.kt:1303)
	at okio.RealBufferedSource.readString(RealBufferedSource.kt:96)
	at okhttp3.ResponseBody.string(ResponseBody.kt:187)
	at com.salesmobile.IndicesApiClient.lambda$fetchIndicesAndSaveValor1$0$com-salesmobile-IndicesApiClient(IndicesApiClient.java:82)
	at com.salesmobile.IndicesApiClient$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)
Caused by: java.net.SocketException: Socket closed
	at java.net.SocketInputStream.read(SocketInputStream.java:188)
	at java.net.SocketInputStream.read(SocketInputStream.java:143)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:989)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:953)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readUntilDataAvailable(ConscryptEngineSocket.java:868)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.read(ConscryptEngineSocket.java:841)
	at okio.InputStreamSource.read(JvmOkio.kt:93)
	at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:128)
	at okio.RealBufferedSource.read(RealBufferedSource.kt:192) 
	at okhttp3.internal.http1.Http1ExchangeCodec$AbstractSource.read(Http1ExchangeCodec.kt:339) 
	at okhttp3.internal.http1.Http1ExchangeCodec$ChunkedSource.read(Http1ExchangeCodec.kt:420) 
	at okhttp3.internal.connection.Exchange$ResponseBodySource.read(Exchange.kt:281) 
	at okio.Buffer.writeAll(Buffer.kt:1303) 
	at okio.RealBufferedSource.readString(RealBufferedSource.kt:96) 
	at okhttp3.ResponseBody.string(ResponseBody.kt:187) 
	at com.salesmobile.IndicesApiClient.lambda$fetchIndicesAndSaveValor1$0$com-salesmobile-IndicesApiClient(IndicesApiClient.java:82) 
	at com.salesmobile.IndicesApiClient$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0) 
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) 
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644) 
	at java.lang.Thread.run(Thread.java:1012) 
2025-07-30 12:55:27.933 16617-19444 HomeFragment            com.salesmobile                      E  Error al obtener cotización desde API: Error de red: timeout